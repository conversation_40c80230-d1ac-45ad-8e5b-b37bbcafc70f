{"timestamp": "2025-05-27T07:55:58.376Z", "environment": "development", "summary": {"total": 28, "passed": 21, "failed": 4, "warnings": 3}, "categories": {"apiConnections": {"tests": [{"name": "Supabase Database Connection", "status": "failed", "message": "Database connection failed: Internal Server Error", "details": {}, "timestamp": "2025-05-27T07:56:05.464Z"}, {"name": "Storage Service", "status": "passed", "message": "Storage service accessible", "details": {"buckets": 1}, "timestamp": "2025-05-27T07:56:05.738Z"}, {"name": "Admin Health Check", "status": "passed", "message": "Admin Health Check responding correctly", "details": {}, "timestamp": "2025-05-27T07:56:05.948Z"}, {"name": "Customers API", "status": "passed", "message": "Customers API responding correctly", "details": {}, "timestamp": "2025-05-27T07:56:06.507Z"}, {"name": "Bookings API", "status": "passed", "message": "Bookings API responding correctly", "details": {}, "timestamp": "2025-05-27T07:56:07.075Z"}, {"name": "Services API", "status": "passed", "message": "Services API responding correctly", "details": {}, "timestamp": "2025-05-27T07:56:07.340Z"}, {"name": "Public Services API", "status": "passed", "message": "Public Services API responding correctly", "details": {}, "timestamp": "2025-05-27T07:56:07.551Z"}, {"name": "Public Products API", "status": "passed", "message": "Public Products API responding correctly", "details": {}, "timestamp": "2025-05-27T07:56:07.770Z"}], "status": "failed"}, "authentication": {"tests": [{"name": "Admin Au<PERSON>ntication", "status": "failed", "message": "No authentication token available for testing", "details": {}, "timestamp": "2025-05-27T07:56:07.771Z"}, {"name": "JWT Token Validation", "status": "passed", "message": "JWT token validation working", "details": {}, "timestamp": "2025-05-27T07:56:07.912Z"}, {"name": "Public API Access", "status": "passed", "message": "Public APIs accessible without authentication", "details": {}, "timestamp": "2025-05-27T07:56:07.996Z"}], "status": "failed"}, "webpages": {"tests": [{"name": "Home Page", "status": "failed", "message": "Home Page error: The operation was aborted.", "details": {}, "timestamp": "2025-05-27T07:56:18.001Z"}, {"name": "About Page", "status": "passed", "message": "About Page loads correctly", "details": {}, "timestamp": "2025-05-27T07:56:22.323Z"}, {"name": "Services Page", "status": "passed", "message": "Services Page loads correctly", "details": {}, "timestamp": "2025-05-27T07:56:23.346Z"}, {"name": "Shop Page", "status": "passed", "message": "Shop Page loads correctly", "details": {}, "timestamp": "2025-05-27T07:56:24.682Z"}, {"name": "Contact Page", "status": "passed", "message": "Contact Page loads correctly", "details": {}, "timestamp": "2025-05-27T07:56:25.138Z"}, {"name": "Book Online Page", "status": "passed", "message": "Book Online Page loads correctly", "details": {}, "timestamp": "2025-05-27T07:56:25.897Z"}, {"name": "Gallery Page", "status": "passed", "message": "Gallery Page loads correctly", "details": {}, "timestamp": "2025-05-27T07:56:26.459Z"}, {"name": "Admin Dashboard", "status": "passed", "message": "Admin Dashboard accessible", "details": {}, "timestamp": "2025-05-27T07:56:27.163Z"}, {"name": "Admin Customers", "status": "passed", "message": "Admin Customers accessible", "details": {}, "timestamp": "2025-05-27T07:56:27.818Z"}, {"name": "Admin Bookings", "status": "passed", "message": "Admin Bookings accessible", "details": {}, "timestamp": "2025-05-27T07:56:27.869Z"}, {"name": "<PERSON><PERSON> Inventory", "status": "passed", "message": "Admin Inventory accessible", "details": {}, "timestamp": "2025-05-27T07:56:29.527Z"}, {"name": "Admin Diagnostics", "status": "passed", "message": "Admin Diagnostics accessible", "details": {}, "timestamp": "2025-05-27T07:56:29.961Z"}], "status": "failed"}, "integrations": {"tests": [{"name": "OneSignal Configuration", "status": "warning", "message": "OneSignal app ID not configured", "details": {}, "timestamp": "2025-05-27T07:56:29.961Z"}, {"name": "Email Configuration", "status": "warning", "message": "Email configuration incomplete or missing", "details": {}, "timestamp": "2025-05-27T07:56:29.962Z"}, {"name": "Square Payment Integration", "status": "warning", "message": "Square payment configuration not found", "details": {}, "timestamp": "2025-05-27T07:56:29.962Z"}], "status": "failed"}, "configuration": {"tests": [{"name": "Required Environment Variables", "status": "failed", "message": "Missing environment variables: NEXT_PUBLIC_SUPABASE_URL, NEXT_PUBLIC_SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY, NEXT_PUBLIC_SITE_URL", "details": {}, "timestamp": "2025-05-27T07:56:04.481Z"}, {"name": "Build Scripts", "status": "passed", "message": "Build and start scripts configured", "details": {}, "timestamp": "2025-05-27T07:56:04.482Z"}], "status": "failed"}}, "overallStatus": "NOT_READY", "recommendations": ["Fix authentication system issues before deployment", "Resolve API connection issues - check Supabase configuration", "Configure all required environment variables", "Consider configuring optional integrations (email, payments, notifications) for full functionality"]}