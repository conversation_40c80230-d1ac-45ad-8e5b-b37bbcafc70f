#!/usr/bin/env node

/**
 * Ocean Soul Sparkles Build with Production Readiness Validation
 * 
 * This script performs a complete build process with integrated validation:
 * 1. Pre-build validation checks
 * 2. Next.js build process
 * 3. Post-build production readiness validation
 * 4. Deployment readiness report
 * 
 * Usage:
 * npm run build:validate
 * or
 * node scripts/build-with-validation.js [--skip-validation] [--environment=production]
 */

import { spawn } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const config = {
  skipValidation: process.argv.includes('--skip-validation'),
  environment: process.argv.includes('--environment=production') ? 'production' : 'development',
  verbose: process.argv.includes('--verbose')
};

// Utility functions
const log = (message, level = 'info') => {
  const timestamp = new Date().toISOString();
  const prefix = level === 'error' ? '❌' : level === 'warning' ? '⚠️' : level === 'success' ? '✅' : 'ℹ️';
  console.log(`${prefix} [${timestamp}] ${message}`);
};

const runCommand = (command, args = [], options = {}) => {
  return new Promise((resolve, reject) => {
    log(`Running: ${command} ${args.join(' ')}`);
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      ...options
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      reject(error);
    });
  });
};

// Pre-build validation
const runPreBuildValidation = async () => {
  log('🔍 Running pre-build validation...', 'info');
  
  try {
    // Check if required files exist
    const requiredFiles = [
      'package.json',
      'next.config.js',
      '.env.local'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(path.join(__dirname, '..', file))) {
        throw new Error(`Required file missing: ${file}`);
      }
    }

    // Check environment variables
    const requiredEnvVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY'
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    if (missingEnvVars.length > 0) {
      throw new Error(`Missing environment variables: ${missingEnvVars.join(', ')}`);
    }

    // Check Node.js version
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion < 18) {
      log(`Warning: Node.js ${nodeVersion} detected. Node.js 18+ recommended for optimal performance.`, 'warning');
    }

    log('✅ Pre-build validation passed', 'success');
    return true;

  } catch (error) {
    log(`❌ Pre-build validation failed: ${error.message}`, 'error');
    return false;
  }
};

// Run Next.js build
const runBuild = async () => {
  log('🏗️ Starting Next.js build...', 'info');
  
  try {
    await runCommand('npm', ['run', 'build']);
    log('✅ Build completed successfully', 'success');
    return true;
  } catch (error) {
    log(`❌ Build failed: ${error.message}`, 'error');
    return false;
  }
};

// Post-build validation
const runPostBuildValidation = async () => {
  log('🔍 Running post-build validation...', 'info');
  
  try {
    // Check if build artifacts exist
    const buildDir = path.join(__dirname, '..', '.next');
    if (!fs.existsSync(buildDir)) {
      throw new Error('Build directory (.next) not found');
    }

    // Check for critical build files
    const criticalFiles = [
      '.next/BUILD_ID',
      '.next/static',
      '.next/server'
    ];

    for (const file of criticalFiles) {
      const filePath = path.join(__dirname, '..', file);
      if (!fs.existsSync(filePath)) {
        throw new Error(`Critical build file missing: ${file}`);
      }
    }

    // Run production readiness check if not skipped
    if (!config.skipValidation) {
      log('🚀 Running production readiness validation...', 'info');
      
      try {
        await runCommand('node', ['scripts/production-readiness-check.js', `--environment=${config.environment}`]);
        log('✅ Production readiness validation passed', 'success');
      } catch (error) {
        log(`⚠️ Production readiness validation had issues: ${error.message}`, 'warning');
        // Don't fail the build for validation warnings, but log them
      }
    }

    log('✅ Post-build validation completed', 'success');
    return true;

  } catch (error) {
    log(`❌ Post-build validation failed: ${error.message}`, 'error');
    return false;
  }
};

// Generate deployment report
const generateDeploymentReport = () => {
  log('📄 Generating deployment report...', 'info');
  
  const report = {
    timestamp: new Date().toISOString(),
    environment: config.environment,
    nodeVersion: process.version,
    buildStatus: 'completed',
    nextSteps: [
      'Review the production readiness report',
      'Test the production build locally with: npm start',
      'Deploy to your hosting platform (Vercel recommended)',
      'Run post-deployment smoke tests'
    ]
  };

  // Add environment-specific recommendations
  if (config.environment === 'production') {
    report.productionRecommendations = [
      'Ensure all environment variables are configured in your hosting platform',
      'Set up monitoring and error tracking',
      'Configure custom domain and SSL certificate',
      'Set up backup and recovery procedures',
      'Test all critical user flows after deployment'
    ];
  }

  const reportPath = path.join(__dirname, '..', 'deployment-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log('\n' + '='.repeat(80));
  console.log('🌊 OCEAN SOUL SPARKLES BUILD COMPLETE 🌊');
  console.log('='.repeat(80));
  console.log(`\n✅ Build Status: ${report.buildStatus.toUpperCase()}`);
  console.log(`📅 Build Date: ${report.timestamp}`);
  console.log(`🌍 Environment: ${report.environment}`);
  console.log(`🟢 Node.js Version: ${report.nodeVersion}`);
  
  console.log('\n📋 NEXT STEPS:');
  report.nextSteps.forEach((step, index) => {
    console.log(`   ${index + 1}. ${step}`);
  });

  if (report.productionRecommendations) {
    console.log('\n🚀 PRODUCTION RECOMMENDATIONS:');
    report.productionRecommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
  }

  console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  console.log('='.repeat(80));
};

// Main execution
const main = async () => {
  console.log('🌊 Ocean Soul Sparkles Build with Validation 🌊');
  console.log(`Environment: ${config.environment}`);
  console.log(`Skip Validation: ${config.skipValidation}`);
  console.log('Starting build process...\n');

  try {
    // Step 1: Pre-build validation
    if (!config.skipValidation) {
      const preValidationPassed = await runPreBuildValidation();
      if (!preValidationPassed) {
        process.exit(1);
      }
    }

    // Step 2: Run build
    const buildPassed = await runBuild();
    if (!buildPassed) {
      process.exit(1);
    }

    // Step 3: Post-build validation
    const postValidationPassed = await runPostBuildValidation();
    if (!postValidationPassed) {
      process.exit(1);
    }

    // Step 4: Generate deployment report
    generateDeploymentReport();

    log('🎉 Build process completed successfully!', 'success');
    process.exit(0);

  } catch (error) {
    log(`💥 Build process failed: ${error.message}`, 'error');
    console.error(error);
    process.exit(1);
  }
};

// Run if executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
